#!/bin/bash
set -e

# --- Цвета для вывода ---
C_RESET='\033[0m'
C_RED='\033[0;31m'
C_GREEN='\033[0;32m'
C_YELLOW='\033[0;33m'
C_CYAN='\033[0;36m'
C_BOLD='\033[1m'

# ==============================================================================
#                  ФУНКЦИИ ГЕНЕРАЦИИ HTML/CSS
# ==============================================================================

JUNK_CSS_RULE_COUNT=300
JUNK_HTML_MAX_DEPTH=6
JUNK_HTML_MAX_CHILDREN=4

generate_realistic_identifier() {
  local style=$((RANDOM % 4))
  local words=("app" "ui" "form" "input" "btn" "wrap" "grid" "item" "box" "nav" "main" "user" "data" "auth" "login" "pass" "field" "group" "widget" "view" "icon" "control" "container" "wrapper" "avatar" "link")
  case $style in
  0)
    local prefixes=("ui" "app" "js" "mod" "el")
    local p1=${prefixes[$RANDOM % ${#prefixes[@]}]}
    local w1=${words[$RANDOM % ${#words[@]}]}
    local w2=${words[$RANDOM % ${#words[@]}]}
    echo "${p1}-${w1}-${w2}"
    ;;
  1)
    local w1=${words[$RANDOM % ${#words[@]}]}
    local w2=${words[$RANDOM % ${#words[@]}]}
    local w3=${words[$RANDOM % ${#words[@]}]}
    echo "${w1}${w2^}${w3^}"
    ;;
  2)
    local len=$((RANDOM % 12 + 8))
    cat /dev/urandom | tr -dc 'a-zA-Z0-9' | head -c $len
    ;;
  *)
    local w1=${words[$RANDOM % ${#words[@]}]}
    local hash=$(cat /dev/urandom | tr -dc 'a-z0-9' | head -c 6)
    echo "${w1}-${hash}"
    ;;
  esac
}
generate_random_var_name() {
  local len=$((RANDOM % 10 + 6))
  echo "--$(cat /dev/urandom | tr -dc 'a-z' | head -c $len)"
}
url_encode_svg() { echo "$1" | sed 's/"/\x27/g' | sed 's/</%3C/g' | sed 's/>/%3E/g' | sed 's/#/%23/g' | sed 's/{/%7B/g' | sed 's/}/%7D/g'; }

generate_junk_html_nodes() {
  local current_depth=$1
  if ((current_depth >= JUNK_HTML_MAX_DEPTH)); then return; fi
  local tags=("div" "p" "span")
  local num_children=$((RANDOM % JUNK_HTML_MAX_CHILDREN + 1))
  for ((i = 0; i < num_children; i++)); do
    local tag=${tags[$RANDOM % ${#tags[@]}]}
    local class=$(generate_realistic_identifier)
    echo "<${tag} class=\"${class}\">$(generate_junk_html_nodes $((current_depth + 1)))</${tag}>"
  done
}
generate_junk_css() {
  local count=$1
  local rules=()
  local colors=("#f44336" "#e91e63" "#9c27b0" "#673ab7" "#3f51b5")
  local units=("px" "rem" "em" "%")
  for ((i = 0; i < count; i++)); do
    local junk_class=$(generate_realistic_identifier)
    local prop1="color: ${colors[$RANDOM % ${#colors[@]}]};"
    local prop2="font-size: $((RANDOM % 14 + 10))px;"
    local prop3="margin: $((RANDOM % 20))${units[$RANDOM % ${#units[@]}]};"
    local prop4="opacity: 0.$((RANDOM % 9 + 1));"
    local props_array=("$prop1" "$prop2" "$prop3" "$prop4")
    local shuffled_props=$(printf "%s\n" "${props_array[@]}" | shuf | tr '\n' ' ')
    rules+=(".${junk_class} { ${shuffled_props} }")
  done
  printf "%s\n" "${rules[@]}"
}

setup_random_theme() {
  local palettes=(
    "#5e72e4;#324cdd;#f6f9fc;#ffffff;#32325d;#8898aa;#dee2e6"
    "#2dce89;#24a46d;#f6f9fc;#ffffff;#32325d;#8898aa;#dee2e6"
    "#11cdef;#0b8ba3;#f6f9fc;#ffffff;#32325d;#8898aa;#dee2e6"
    "#fb6340;#fa3a0e;#f6f9fc;#ffffff;#32325d;#8898aa;#dee2e6"
    "#6772e5;#5469d4;#f6f9fc;#ffffff;#32325d;#8898aa;#dee2e6"
  )
  local font_stacks=(
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif"
    "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif"
    "'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
  )

  local selected_palette=${palettes[$RANDOM % ${#palettes[@]}]}
  IFS=';' read -r VAR_PRIMARY_COLOR VAR_HOVER_COLOR VAR_BG_COLOR VAR_CARD_COLOR VAR_TEXT_COLOR VAR_TEXT_LIGHT_COLOR VAR_BORDER_COLOR <<<"$selected_palette"
  VAR_FONT_SANS_SERIF=${font_stacks[$RANDOM % ${#font_stacks[@]}]}

  CSS_VAR_PRIMARY=$(generate_random_var_name)
  CSS_VAR_HOVER=$(generate_random_var_name)
  CSS_VAR_BG=$(generate_random_var_name)
  CSS_VAR_CARD_BG=$(generate_random_var_name)
  CSS_VAR_TEXT=$(generate_random_var_name)
  CSS_VAR_TEXT_LIGHT=$(generate_random_var_name)
  CSS_VAR_BORDER=$(generate_random_var_name)
  CSS_VAR_FONT=$(generate_random_var_name)
}

generate_selfsteal_form() {
  local output_dir="$1"
  setup_random_theme

  local html_filename="${output_dir}/index.html"
  local css_filename="${output_dir}/styles.css"
  local class_container=$(generate_realistic_identifier)
  local class_form_wrapper=$(generate_realistic_identifier)
  local class_title=$(generate_realistic_identifier)
  local class_input_email=$(generate_realistic_identifier)
  local class_input_pass=$(generate_realistic_identifier)
  local class_button=$(generate_realistic_identifier)
  local class_junk_wrapper=$(generate_realistic_identifier)
  local name_user=$(generate_realistic_identifier)
  local name_pass=$(generate_realistic_identifier)
  local action_url="/gateway/$(generate_realistic_identifier)/auth"
  local class_extra_links=$(generate_realistic_identifier)
  local class_forgot_link=$(generate_realistic_identifier)

  local svg_email_icon_raw='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="'${VAR_TEXT_LIGHT_COLOR}'"><path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/></svg>'
  local svg_lock_icon_raw='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="'${VAR_TEXT_LIGHT_COLOR}'"><path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"/></svg>'
  local encoded_email_icon=$(url_encode_svg "$svg_email_icon_raw")
  local encoded_lock_icon=$(url_encode_svg "$svg_lock_icon_raw")

  local main_content_html="<div class=\"${class_container}\"><div class=\"${class_form_wrapper}\"><h2 class=\"${class_title}\">Login</h2><form action=\"${action_url}\" method=\"post\"><input type=\"email\" name=\"${name_user}\" class=\"${class_input_email}\" placeholder=\"Email\" required><input type=\"password\" name=\"${name_pass}\" class=\"${class_input_pass}\" placeholder=\"Password\" required><div class=\"${class_extra_links}\"><a href=\"#\" class=\"${class_forgot_link}\">Forgot Password?</a></div><button type=\"submit\" class=\"${class_button}\">Login</button></form></div></div>"
  local junk_html_block="<div class=\"${class_junk_wrapper}\">$(generate_junk_html_nodes 0)</div>"

  local core_css="
:root { ${CSS_VAR_PRIMARY}: ${VAR_PRIMARY_COLOR}; ${CSS_VAR_HOVER}: ${VAR_HOVER_COLOR}; ${CSS_VAR_BG}: ${VAR_BG_COLOR}; ${CSS_VAR_CARD_BG}: ${VAR_CARD_COLOR}; ${CSS_VAR_TEXT}: ${VAR_TEXT_COLOR}; ${CSS_VAR_TEXT_LIGHT}: ${VAR_TEXT_LIGHT_COLOR}; ${CSS_VAR_BORDER}: ${VAR_BORDER_COLOR}; ${CSS_VAR_FONT}: ${VAR_FONT_SANS_SERIF}; }
html { font-family: var(${CSS_VAR_FONT}); font-size: 16px; }
body { margin: 0; background-color: var(${CSS_VAR_BG}); display: flex; align-items: center; justify-content: center; min-height: 100vh; }
"
  local component_pool=()
  component_pool+=(".${class_container} { width: 100%; max-width: 450px; padding: 1rem; }")
  component_pool+=(".${class_form_wrapper} { background-color: var(${CSS_VAR_CARD_BG}); padding: 3rem; border-radius: 12px; box-shadow: 0 7px 30px rgba(50, 50, 93, 0.1), 0 3px 8px rgba(0, 0, 0, 0.07); text-align: center; }")
  component_pool+=(".${class_title} { font-size: 1.5rem; font-weight: 600; color: var(${CSS_VAR_TEXT_LIGHT}); margin: 0 0 2.5rem 0; text-transform: uppercase; letter-spacing: 1px; }")
  component_pool+=(".${class_input_email}, .${class_input_pass} { width: 100%; box-sizing: border-box; font-size: 1rem; padding: 0.9rem 1rem 0.9rem 3.2rem; margin-bottom: 1.25rem; border: 1px solid var(${CSS_VAR_BORDER}); border-radius: 8px; background-repeat: no-repeat; background-position: left 1.2rem center; background-size: 20px; transition: all 0.15s ease; }")
  component_pool+=(".${class_input_email}:focus, .${class_input_pass}:focus { outline: none; border-color: var(${CSS_VAR_PRIMARY}); box-shadow: 0 0 0 3px color-mix(in srgb, var(${CSS_VAR_PRIMARY}) 20%, transparent); }")
  component_pool+=(".${class_input_email} { background-image: url('data:image/svg+xml,${encoded_email_icon}'); }")
  component_pool+=(".${class_input_pass} { background-image: url('data:image/svg+xml,${encoded_lock_icon}'); }")
  component_pool+=(".${class_extra_links} { text-align: right; margin-bottom: 1.5rem; }")
  component_pool+=(".${class_forgot_link} { color: var(${CSS_VAR_PRIMARY}); text-decoration: none; font-size: 0.9rem; }")
  component_pool+=(".${class_forgot_link}:hover { text-decoration: underline; }")
  component_pool+=(".${class_button} { width: 100%; box-sizing: border-box; padding: 1rem; font-size: 1rem; font-weight: 600; color: #fff; background-image: linear-gradient(35deg, var(${CSS_VAR_PRIMARY}), var(${CSS_VAR_HOVER})); border: none; border-radius: 8px; cursor: pointer; transition: transform 0.2s, box-shadow 0.2s; box-shadow: 0 4px 15px color-mix(in srgb, var(${CSS_VAR_PRIMARY}) 40%, transparent); }")
  component_pool+=(".${class_button}:hover { transform: translateY(-2px); box-shadow: 0 7px 25px color-mix(in srgb, var(${CSS_VAR_PRIMARY}) 50%, transparent); }")
  component_pool+=(".${class_junk_wrapper} { display: none !important; }")

  local junk_css_rules=$(generate_junk_css $JUNK_CSS_RULE_COUNT)

  echo "${core_css}" >"${css_filename}"
  # Ссылаемся на CSS-файл по относительному пути
  printf "%s\n%s" "$(printf "%s\n" "${component_pool[@]}")" "$junk_css_rules" | shuf >>"${css_filename}"

  cat <<EOF >"$html_filename"
<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Login</title><link rel="stylesheet" href="styles.css"></head><body>$(if ((RANDOM % 2 == 0)); then echo "$main_content_html $junk_html_block"; else echo "$junk_html_block $main_content_html"; fi)</body></html>
EOF
}

# ==============================================================================
#                  ФУНКЦИИ УСТАНОВКИ И УПРАВЛЕНИЯ
# ==============================================================================

check_deps() {
  echo -e "${C_CYAN}Checking dependencies...${C_RESET}"
  local missing_deps=0
  for cmd in docker shuf; do
    if ! command -v "$cmd" &>/dev/null; then
      echo -e "${C_RED}Error: '$cmd' not found. Please install it.${C_RESET}"
      missing_deps=1
    fi
  done

  # Проверяем docker compose (v2) или docker-compose (v1)
  if command -v "docker" &>/dev/null && docker compose version &>/dev/null; then
    COMPOSE_CMD="docker compose"
  elif command -v "docker-compose" &>/dev/null; then
    COMPOSE_CMD="docker-compose"
  else
    echo -e "${C_RED}Error: 'docker-compose' or 'docker compose' not found. Please install Docker and Docker Compose.${C_RESET}"
    missing_deps=1
  fi

  if [ "$missing_deps" -ne 0 ]; then
    exit 1
  fi
  echo -e "${C_GREEN}All dependencies are installed.${C_RESET}"
}

create_haproxy_config() {
  local domain="$1"
  local config_path="$2"

  cat <<EOF >"${config_path}"
global
    log stdout format raw local0
    daemon
    tune.bufsize 65536
    tune.ssl.cachesize 131072
    tune.ssl.lifetime 86400 # 1 сутки
    ssl-default-bind-curves X25519:prime256v1:secp384r1
    ssl-default-bind-ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-CHACHA20-POLY1305
    ssl-default-bind-options no-tls-tickets

defaults
    mode http
    log global
    option httplog
    timeout connect 5s
    timeout client 50s
    timeout server 50s

cache STATIC
    total-max-size 64
    max-object-size 2097152
    max-age 60

frontend ft_main # Биндим на 127.0.0.1:9443, чтобы доступ был только через xray или другой прокси
    bind 127.0.0.1:9443 ssl crt /usr/local/etc/haproxy/certs/full.pem alpn h2,http/1.1 accept-proxy ssl-min-ver TLSv1.2 ssl-max-ver TLSv1.3

    ### cache ###
    http-request cache-use STATIC
    http-response cache-store STATIC

    ### ограничиваем SNI ###
    acl host_ok ssl_fc_sni -i ${domain}
    http-request silent-drop unless host_ok

    ### WebSocket ###
    acl is_ws req.hdr(Upgrade) -i websocket
    http-request set-header Connection upgrade if is_ws

    ### статика ###
    acl is_index path -i / /index.html
    http-request return status 200 content-type text/html file /usr/local/etc/haproxy/html/index.html if is_index

    acl is_css path -i /styles.css
    http-request return status 200 content-type text/css file /usr/local/etc/haproxy/html/styles.css if is_css

    ### всё прочее → 404 ###
    http-request return status 404
EOF
}

create_docker_compose() {
  local cert_path="$1"
  local compose_path="$2"

  cat <<EOF >"${compose_path}"
services:
  haproxy:
    image: haproxy:2.8
    container_name: selfsteal-haproxy
    network_mode: "host"
    volumes:
      - ./haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
      - ${cert_path}:/usr/local/etc/haproxy/certs/full.pem:ro
      - ./html:/usr/local/etc/haproxy/html:ro
    restart: unless-stopped
EOF
}

main() {
  clear
  echo -e "${C_BOLD}${C_CYAN}"
  echo "=========================================="
  echo "    Self-Steal Site Installer"
  echo "=========================================="
  echo -e "${C_RESET}"

  check_deps

  echo
  echo -e "${C_YELLOW}Пожалуйста, введите данные для установки.${C_RESET}"

  # Запрос директории для установки
  read -p "Укажите директорию для установки (например, /root/mysite): " INSTALL_DIR
  if [ -z "$INSTALL_DIR" ]; then
    echo -e "${C_RED}Директория не может быть пустой. Выход.${C_RESET}"
    exit 1
  fi
  mkdir -p "$INSTALL_DIR"
  cd "$INSTALL_DIR"
  INSTALL_DIR=$(pwd) # Получаем абсолютный путь

  # Запрос домена
  read -p "Введите ваше доменное имя (например, mydomain.com): " USER_DOMAIN
  if [ -z "$USER_DOMAIN" ]; then
    echo -e "${C_RED}Домен не может быть пустым. Выход.${C_RESET}"
    exit 1
  fi

  # Запрос пути к сертификату
  while true; do
    read -p "Введите ПОЛНЫЙ путь к файлу сертификата (full.pem или fullchain.pem): " USER_CERT_PATH
    if [ -f "$USER_CERT_PATH" ]; then
      break
    else
      echo -e "${C_RED}Файл не найден по пути '${USER_CERT_PATH}'. Пожалуйста, попробуйте еще раз.${C_RESET}"
    fi
  done

  echo
  echo -e "${C_CYAN}--- Начинаю генерацию файлов ---${C_RESET}"

  # Создание директории для HTML
  mkdir -p html
  echo -e "  [${C_GREEN}✔${C_RESET}] Создана директория: ${INSTALL_DIR}/html"

  # Генерация HTML и CSS
  generate_selfsteal_form "html"
  echo -e "  [${C_GREEN}✔${C_RESET}] Сгенерированы index.html и styles.css"

  # Создание haproxy.cfg
  create_haproxy_config "$USER_DOMAIN" "haproxy.cfg"
  echo -e "  [${C_GREEN}✔${C_RESET}] Создан конфигурационный файл haproxy.cfg"

  # Создание docker-compose.yml
  create_docker_compose "$USER_CERT_PATH" "docker-compose.yml"
  echo -e "  [${C_GREEN}✔${C_RESET}] Создан конфигурационный файл docker-compose.yml"

  echo
  echo -e "${C_GREEN}${C_BOLD}Настройка завершена!${C_RESET}"
  echo -e "Все файлы созданы в директории: ${C_YELLOW}${INSTALL_DIR}${C_RESET}"
  echo

  read -p "Хотите запустить контейнер сейчас? (y/n): " launch_confirm
  if [[ "$launch_confirm" =~ ^[Yy]$ ]]; then
    echo -e "${C_CYAN}Запускаю Docker контейнер...${C_RESET}"
    if ${COMPOSE_CMD} up -d; then
      echo -e "\n${C_GREEN}${C_BOLD}Контейнер успешно запущен!${C_RESET}"
      echo -e "------------------------------------------------------------------"
      echo -e "Ваш self-steal сайт настроен для домена: ${C_YELLOW}${USER_DOMAIN}${C_RESET}"
      echo -e "Он слушает на локальном адресе: ${C_YELLOW}127.0.0.1:9443${C_RESET}"
      echo
      echo -e "${C_BOLD}ВАЖНО:${C_RESET} Вам необходимо настроить ваш основной веб-сервер (Xray, Nginx и т.д.)"
      echo -e "для проксирования трафика с вашего домена на ${C_YELLOW}127.0.0.1:9443${C_RESET}."
      echo
      echo -e "${C_CYAN}Команды для управления (выполнять в ${INSTALL_DIR}):${C_RESET}"
      echo -e "  - Остановить:      ${C_YELLOW}${COMPOSE_CMD} down${C_RESET}"
      echo -e "  - Запустить:        ${C_YELLOW}${COMPOSE_CMD} up -d${C_RESET}"
      echo -e "  - Посмотреть логи: ${C_YELLOW}${COMPOSE_CMD} logs -f${C_RESET}"
      echo -e "------------------------------------------------------------------"
    else
      echo -e "${C_RED}Не удалось запустить Docker контейнер. Проверьте логи с помощью '${COMPOSE_CMD} logs'.${C_RESET}"
    fi
  else
    echo -e "${C_YELLOW}Запуск отменен. Вы можете запустить контейнер вручную, перейдя в директорию ${INSTALL_DIR} и выполнив '${COMPOSE_CMD} up -d'${C_RESET}"
  fi
}

main "$@"
